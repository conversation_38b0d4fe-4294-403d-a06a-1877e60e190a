/**
 * Centralized Type Exports
 * 
 * This file provides organized exports for all types in the application.
 * Types are separated by layer to maintain clear boundaries:
 * 
 * - Database types (snake_case) - for database operations
 * - API types (camelCase) - for API request/response handling  
 * - Frontend types (camelCase) - for UI components and state management
 * - Legacy types - for backward compatibility (will be phased out)
 */

// === LAYER-SEPARATED TYPES (RECOMMENDED) ===

// Database layer types (snake_case)
export * from './database';

// API layer types (camelCase)
export * from './api';

// Frontend layer types (camelCase with UI state)
export * from './frontend';

// === DOMAIN-SPECIFIC TYPES ===

// Calculation engine types
export * from './calculation';

// AI service types
export * from './ai-parsing';
export * from './ai-turbo';

// === LEGACY TYPES (DEPRECATED) ===
// These are kept for backward compatibility but should be migrated to the new layer-separated types

// Legacy task master types (mixed naming conventions)
// NOTE: Kept for backward compatibility - new code should use layer-separated types
export * from './task-master';
