import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Project } from '@/lib/database/task-master-service';
import { TaskMaster, TaskMasterColumn, Task } from '@/lib/types/task-master';

// Simple client-side cache for project data
interface CacheEntry {
  data: TaskMaster[];
  timestamp: number;
  includeDetails: boolean;
}

const projectCache = new Map<string, CacheEntry>();
const CACHE_TTL = 2 * 60 * 1000; // 2 minutes cache TTL

// Convert backend Project to frontend TaskMaster format
function projectToTask(project: Project): TaskMaster {
  return {
    id: project.id.toString(),
    title: project.title,
    description: project.description || '',
    status: project.status,
    priority: project.priority,
    progress: project.progress,
    dueDate: project.dueDate,
    completedDate: project.completedDate,
    visibility: project.visibility,
    assignee: project.assignedTo ? {
      name: 'User', // We'll need to get this from user data
      avatar: ''
    } : undefined,
    details: project.fullDescription,
    // Creator information for ownership validation
    createdBy: project.createdBy,
    createdByUsername: project.createdByUsername,
    projectDetails: {
      fullDescription: project.fullDescription || '',
      tags: project.tags || [],
      teamMembers: (project.teamMembers || []).map(member => ({
        ...member,
        name: member.username || member.email || 'Unknown'
      })),
      files: project.files || [],
      comments: (project.comments || []).map(comment => ({
        id: comment.id.toString(),
        author: {
          id: comment.authorId?.toString() || '0',
          name: comment.authorUsername || 'Unknown',
          avatar: '',
          role: 'member',
          email: comment.authorEmail
        },
        content: comment.content,
        createdAt: comment.createdAt
      })),
      eventLog: [] // We'll populate this from activity data
    },
    // Include hierarchical tasks
    tasks: project.tasks || []
  };
}

// Convert tasks to column format
function tasksToColumns(projects: Project[]): TaskMasterColumn[] {
  const todoTasks = projects.filter(p => p.status === 'todo').map(projectToTask);
  const inProgressTasks = projects.filter(p => p.status === 'inProgress').map(projectToTask);
  const completedTasks = projects.filter(p => p.status === 'completed').map(projectToTask);

  return [
    {
      id: 'todo',
      title: 'To Do',
      icon: 'Inbox',
      variant: 'brand',
      count: todoTasks.length,
      tasks: todoTasks
    },
    {
      id: 'inProgress',
      title: 'In Progress',
      icon: 'Clock',
      variant: 'warning',
      count: inProgressTasks.length,
      tasks: inProgressTasks
    },
    {
      id: 'completed',
      title: 'Completed',
      icon: 'CheckCircle',
      variant: 'success',
      count: completedTasks.length,
      tasks: completedTasks
    }
  ];
}

export function useTaskMaster() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [taskColumns, setTaskColumns] = useState<TaskMasterColumn[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fetchPromise, setFetchPromise] = useState<Promise<void> | null>(null);
  const { toast } = useToast();

  // Fetch projects from backend with caching, selective loading and request deduplication
  const fetchProjects = useCallback(async (includeDetails: boolean = false, forceRefresh: boolean = false) => {
    // Check cache first (unless force refresh is requested)
    if (!forceRefresh) {
      const cacheKey = includeDetails ? 'detailed' : 'basic';
      const cached = projectCache.get(cacheKey);
      const now = Date.now();

      if (cached && (now - cached.timestamp) < CACHE_TTL) {
        setProjects(cached.data);
        setTaskColumns(tasksToColumns(cached.data));
        return;
      }
    }

    // Request deduplication: if there's already a fetch in progress, return that promise
    if (fetchPromise) {
      return fetchPromise;
    }

    const promise = (async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Only include details when explicitly requested (e.g., for drawer operations)
        const url = includeDetails
          ? '/api/task-master/projects?includeDetails=true'
          : '/api/task-master/projects';

        const response = await fetch(url, {
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });
        const data = await response.json();

        if (response.ok) {
          const projects = data.data.projects;
          setProjects(projects);
          setTaskColumns(tasksToColumns(projects));

          // Cache the result
          const cacheKey = includeDetails ? 'detailed' : 'basic';
          projectCache.set(cacheKey, {
            data: projects,
            timestamp: Date.now(),
            includeDetails
          });

          // Clean up old cache entries
          if (projectCache.size > 5) {
            const now = Date.now();
            for (const [key, entry] of projectCache.entries()) {
              if ((now - entry.timestamp) >= CACHE_TTL) {
                projectCache.delete(key);
              }
            }
          }
        } else {
          setError(data.error || 'Failed to fetch projects');
          toast({
            title: "Error",
            description: data.error || "Failed to fetch projects",
            variant: "destructive"
          });
        }
      } catch (error) {
        const errorMessage = 'An unexpected error occurred';
        setError(errorMessage);
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
        setFetchPromise(null); // Clear the promise when done
      }
    })();

    setFetchPromise(promise);
    return promise;
  }, [fetchPromise, toast]);

  // Create new project with optimistic UI
  const createProject = useCallback(async (projectData: {
    title: string;
    description?: string;
    fullDescription?: string;
    status?: 'todo' | 'inProgress' | 'completed';
    priority?: 'low' | 'medium' | 'high';
    progress?: number;
    dueDate?: string;
    visibility?: 'public' | 'private';
    tags?: string[];
  }) => {
    // Generate optimistic project with temporary ID
    const optimisticProject: TaskMaster = {
      id: `temp-${Date.now()}`,
      title: projectData.title,
      description: projectData.description || '',
      status: projectData.status || 'todo',
      priority: projectData.priority || 'medium',
      dueDate: projectData.dueDate,
      visibility: projectData.visibility || 'public',
      assignedTo: projectData.assignedTo,
      createdBy: 0, // Will be set by server
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      progress: 0,
      taskCount: 0,
      completedTaskCount: 0,
      projectDetails: {
        fullDescription: projectData.fullDescription || '',
        eventLog: '',
        fullDescriptionLastEditedBy: null,
        fullDescriptionLastEditedAt: null,
        eventLogLastEditedBy: null,
        eventLogLastEditedAt: null
      }
    };

    // Optimistic update: immediately add to UI
    setProjects(prev => [optimisticProject, ...prev]);
    setTaskColumns(prev => {
      const todoColumn = prev.find(col => col.id === 'todo');
      if (todoColumn) {
        return prev.map(col =>
          col.id === 'todo'
            ? { ...col, tasks: [optimisticProject, ...col.tasks] }
            : col
        );
      }
      return prev;
    });

    try {
      const response = await fetch('/api/task-master/projects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(projectData)
      });

      const data = await response.json();

      if (response.ok) {
        const realProject = data.data.project;

        // Generate display title using the real project ID
        const displayTitle = `Task #${realProject.id}`;
        const updatedRealProject = {
          ...realProject,
          title: displayTitle
        };

        // Convert to TaskMaster format for frontend
        const updatedTaskMaster = projectToTask(updatedRealProject);

        // Replace optimistic project with real project data
        setProjects(prev => prev.map(p =>
          p.id === optimisticProject.id ? updatedRealProject : p
        ));
        setTaskColumns(prev => prev.map(col => ({
          ...col,
          tasks: col.tasks.map(task =>
            task.id === optimisticProject.id ? updatedTaskMaster : task
          )
        })));

        toast({
          title: "Success",
          description: "Project created successfully"
        });

        return updatedRealProject;
      } else {
        // Rollback optimistic update on error
        setProjects(prev => prev.filter(p => p.id !== optimisticProject.id));
        setTaskColumns(prev => prev.map(col => ({
          ...col,
          tasks: col.tasks.filter(task => task.id !== optimisticProject.id)
        })));

        toast({
          title: "Error",
          description: data.error || "Failed to create project",
          variant: "destructive"
        });
        return null;
      }
    } catch (error) {
      // Rollback optimistic update on error
      setProjects(prev => prev.filter(p => p.id !== optimisticProject.id));
      setTaskColumns(prev => prev.map(col => ({
        ...col,
        tasks: col.tasks.filter(task => task.id !== optimisticProject.id)
      })));

      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
      return null;
    }
  }, [toast]);

  // Update project
  const updateProject = useCallback(async (projectId: string, updateData: {
    title?: string;
    description?: string;
    fullDescription?: string;
    status?: 'todo' | 'inProgress' | 'completed';
    priority?: 'low' | 'medium' | 'high';
    progress?: number;
    dueDate?: string;
  }) => {
    try {
      // Invalidate cache when status changes to ensure fresh progress calculation
      if (updateData.status) {
        projectCache.clear();
      }
      const response = await fetch(`/api/task-master/projects/${projectId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      });
      
      const data = await response.json();
      
      if (response.ok) {
        // Use the backend response data instead of optimistic update to ensure correct progress calculation
        const updatedProject = data.data.project;
        setProjects(prev => {
          const updated = prev.map(p =>
            p.id.toString() === projectId
              ? updatedProject
              : p
          );
          setTaskColumns(tasksToColumns(updated));
          return updated;
        });

        return updatedProject;
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to update project",
          variant: "destructive"
        });
        return null;
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
      return null;
    }
  }, [toast]);

  // Move task between columns (drag and drop)
  const moveTask = useCallback(async (taskId: string, fromColumnId: string, toColumnId: string) => {
    // Check if movement skips intermediate stages and show informational toast
    const columnOrder = ['todo', 'inProgress', 'completed'];
    const fromIndex = columnOrder.indexOf(fromColumnId);
    const toIndex = columnOrder.indexOf(toColumnId);

    // Show informational toast for multi-stage movements (but don't block them)
    if (Math.abs(fromIndex - toIndex) > 1) {
      toast({
        title: "Multi-stage Movement",
        description: "Single step task transferal is recommended",
        variant: "default", // Changed from destructive to default (white/less priority)
      });
    }

    // Update project status (now allows all movements)
    const success = await updateProject(taskId, {
      status: toColumnId as 'todo' | 'inProgress' | 'completed'
    });

    // Visual feedback is provided by animations - no success toast needed
    return !!success;
  }, [updateProject, toast]);

  // Add comment to project (optimized - no full refresh needed)
  const addComment = useCallback(async (projectId: string, content: string) => {
    try {
      const response = await fetch(`/api/task-master/projects/${projectId}/comments`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ content })
      });

      const data = await response.json();

      if (response.ok) {
        toast({
          title: "Success",
          description: "Comment added successfully"
        });

        // Note: Comments are typically loaded on-demand when drawer opens
        // No need to refresh entire project list for a comment addition

        return data.data.comment;
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to add comment",
          variant: "destructive"
        });
        return null;
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
      return null;
    }
  }, [toast]);

  // Delete project
  const deleteProject = useCallback(async (projectId: string) => {
    try {
      const response = await fetch(`/api/task-master/projects/${projectId}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (response.ok) {
        toast({
          title: "Success",
          description: "Task deleted successfully"
        });

        // Remove from local state
        setProjects(prev => {
          const updated = prev.filter(p => p.id.toString() !== projectId);
          setTaskColumns(tasksToColumns(updated));
          return updated;
        });

        return true;
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to delete task",
          variant: "destructive"
        });
        return false;
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  // Create new task with optimistic progress update
  const createTask = useCallback(async (projectId: string, taskData: {
    title: string;
    description?: string;
    status?: 'todo' | 'inProgress' | 'completed';
    priority?: 'low' | 'medium' | 'high';
    dueDate?: string;
    assignedTo?: number;
    parentTaskId?: string | number;
  }) => {
    // Skip optimistic updates for temporary tasks to prevent progress corruption
    const isTemporaryTask = taskData.parentTaskId &&
      typeof taskData.parentTaskId === 'string' &&
      taskData.parentTaskId.startsWith('temp-');

    if (!isTemporaryTask) {
      // Only apply optimistic updates for real tasks
      setProjects(prev => prev.map(project =>
        project.id === projectId
          ? {
              ...project,
              taskCount: project.taskCount + 1,
              // FIXED: Safe progress calculation to prevent NaN
              progress: (project.taskCount + 1) > 0
                ? Math.round((project.completedTaskCount / (project.taskCount + 1)) * 100)
                : 0
            }
          : project
      ));
    }

    setTaskColumns(prev => prev.map(col => ({
      ...col,
      tasks: col.tasks.map(task =>
        task.id === projectId
          ? {
              ...task,
              taskCount: task.taskCount + 1,
              progress: Math.round((task.completedTaskCount / (task.taskCount + 1)) * 100)
            }
          : task
      )
    })));

    try {
      // Check if this is a temporary task creation
      const isTemporaryParent = taskData.parentTaskId && (
        // String IDs starting with "temp-" (from AI parsing)
        (typeof taskData.parentTaskId === 'string' && taskData.parentTaskId.startsWith('temp-')) ||
        // Numeric IDs that are timestamps (from optimistic creation) - these are > 1000000000000 (year 2001+)
        (typeof taskData.parentTaskId === 'number' && taskData.parentTaskId > 1000000000000) ||
        // String representations of timestamp IDs
        (typeof taskData.parentTaskId === 'string' && !isNaN(parseInt(taskData.parentTaskId)) && parseInt(taskData.parentTaskId) > 1000000000000)
      );

      if (isTemporaryParent) {
        // This is a temporary subtask under a temporary parent - handle in optimistic state only
        // Don't make API call yet, this will be handled during batch submission
        console.log('✅ Creating temporary subtask under temporary parent:', taskData.parentTaskId, typeof taskData.parentTaskId);
        return { success: true, isTemporary: true };
      }

      // For real tasks or subtasks under real parents, proceed with API call
      const payload: any = {
        projectId: parseInt(projectId),
        title: taskData.title,
        description: taskData.description,
        status: taskData.status || 'todo',
        priority: taskData.priority || 'medium'
      };

      // Add optional fields if provided
      if (taskData.dueDate) payload.dueDate = taskData.dueDate;
      if (taskData.assignedTo) payload.assignedTo = taskData.assignedTo;

      // Only include parentTaskId if it's a valid numeric ID (for real subtasks)
      if (taskData.parentTaskId) {
        // Handle both string and numeric parent IDs
        let parentId: number;

        if (typeof taskData.parentTaskId === 'string') {
          // Check if it's a temporary ID (should have been caught above)
          if (taskData.parentTaskId.startsWith('temp-')) {
            console.error('❌ Temporary parent ID should have been handled earlier:', taskData.parentTaskId);
            throw new Error('Temporary parent ID in real task creation');
          }
          parentId = parseInt(taskData.parentTaskId);
        } else {
          parentId = taskData.parentTaskId;
        }

        if (!isNaN(parentId) && parentId > 0) {
          payload.parentTaskId = parentId;
          console.log('✅ Creating real subtask under parent ID:', parentId);
        } else {
          console.error('❌ Invalid parent task ID for real subtask:', taskData.parentTaskId);
          throw new Error('Invalid parent task ID');
        }
      } else {
        console.log('✅ Creating main task (no parent)');
      }

      console.log('🔧 Making API call to create task with payload:', JSON.stringify(payload, null, 2));

      const response = await fetch('/api/task-master/tasks', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      console.log('🔧 API response status:', response.status);
      const responseData = await response.json();
      console.log('🔧 API response data:', responseData);

      if (response.ok) {
        // Sync with server response for accurate progress
        const serverTask = responseData.data.task;

        // Update project with accurate task count and progress from server
        setProjects(prev => prev.map(project =>
          project.id === projectId
            ? {
                ...project,
                taskCount: serverTask.projectTaskCount || project.taskCount,
                progress: serverTask.projectProgress || project.progress
              }
            : project
        ));

        setTaskColumns(prev => prev.map(col => ({
          ...col,
          tasks: col.tasks.map(task =>
            task.id === projectId
              ? {
                  ...task,
                  taskCount: serverTask.projectTaskCount || task.taskCount,
                  progress: serverTask.projectProgress || task.progress
                }
              : task
          )
        })));

        toast({
          title: "Success",
          description: "Task created successfully"
        });

        return serverTask;
      } else {
        // Rollback optimistic update on error
        setProjects(prev => prev.map(project =>
          project.id === projectId
            ? {
                ...project,
                taskCount: Math.max(0, project.taskCount - 1),
                progress: project.taskCount > 1
                  ? Math.round((project.completedTaskCount / (project.taskCount - 1)) * 100)
                  : 0
              }
            : project
        ));

        setTaskColumns(prev => prev.map(col => ({
          ...col,
          tasks: col.tasks.map(task =>
            task.id === projectId
              ? {
                  ...task,
                  taskCount: Math.max(0, task.taskCount - 1),
                  progress: task.taskCount > 1
                    ? Math.round((task.completedTaskCount / (task.taskCount - 1)) * 100)
                    : 0
                }
              : task
          )
        })));

        console.error('❌ API call failed:', response.status, responseData);
        toast({
          title: "Error",
          description: responseData.error || "Failed to create task",
          variant: "destructive"
        });
        return null;
      }
    } catch (error) {
      // Rollback optimistic update on error
      setProjects(prev => prev.map(project =>
        project.id === projectId
          ? {
              ...project,
              taskCount: Math.max(0, project.taskCount - 1),
              // FIXED: Safe progress calculation to prevent NaN
              progress: (project.taskCount - 1) > 0
                ? Math.round((project.completedTaskCount / (project.taskCount - 1)) * 100)
                : 0
            }
          : project
      ));

      setTaskColumns(prev => prev.map(col => ({
        ...col,
        tasks: col.tasks.map(task =>
          task.id === projectId
            ? {
                ...task,
                taskCount: Math.max(0, task.taskCount - 1),
                // FIXED: Safe progress calculation to prevent NaN
                progress: (task.taskCount - 1) > 0
                  ? Math.round((task.completedTaskCount / (task.taskCount - 1)) * 100)
                  : 0
              }
            : task
        )
      })));

      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
      return null;
    }
  }, [toast]);

  // Update task (optimized - progress updates handled by project-level operations)
  const updateTask = useCallback(async (taskId: number, updateData: {
    title?: string;
    description?: string;
    status?: 'todo' | 'inProgress' | 'completed';
    priority?: 'low' | 'medium' | 'high';
    dueDate?: string;
    assignedTo?: number;
  }) => {
    try {
      const response = await fetch(`/api/task-master/tasks/${taskId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      });

      const data = await response.json();

      if (response.ok) {
        // NO TOAST - Progress bar provides visual feedback
        // Task updates are handled by optimistic updates for instant feedback

        return data.data.task;
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to update task",
          variant: "destructive"
        });
        return null;
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
      return null;
    }
  }, [toast]);

  // Delete task (optimized - no full refresh needed)
  const deleteTask = useCallback(async (taskId: number): Promise<void> => {
    try {
      const response = await fetch(`/api/task-master/tasks/${taskId}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (response.ok) {
        toast({
          title: "Success",
          description: "Task deleted successfully"
        });

        // Note: Task deletion affects project progress, but this is typically
        // handled at the project level. For immediate UI feedback, the calling
        // component should handle optimistic updates if needed.
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to delete task",
          variant: "destructive"
        });
        throw new Error(data.error || "Failed to delete task");
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
      throw error;
    }
  }, [toast]);

  // Load projects on mount
  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  // File management functions (optimized - no full refresh needed)
  const handleFileUploaded = useCallback(async () => {
    // Note: File uploads are typically handled in drawer context
    // where file lists are loaded on-demand. No need to refresh
    // entire project list for file operations.
  }, []);

  const handleFileDeleted = useCallback(async (fileId: number) => {
    // Note: File deletions are typically handled in drawer context
    // where file lists are loaded on-demand. No need to refresh
    // entire project list for file operations.
  }, []);

  // Team management functions (optimized - no full refresh needed)
  const handleTeamUpdated = useCallback(async () => {
    // Note: Team updates are typically handled in drawer context
    // where team data is loaded on-demand. No need to refresh
    // entire project list for team operations.
  }, []);

  // Cache invalidation function
  const invalidateCache = useCallback(() => {
    projectCache.clear();
  }, []);

  // Force refresh function that bypasses cache
  const refreshProjects = useCallback(async (includeDetails: boolean = false) => {
    return fetchProjects(includeDetails, true);
  }, [fetchProjects]);

  // Add temporary card for optimistic UI (used by Turbo mode)
  const addTemporaryCard = useCallback((tempCard: TaskMaster) => {
    setTaskColumns(prev => {
      const todoColumn = prev.find(col => col.id === 'todo');
      if (todoColumn) {
        return prev.map(col =>
          col.id === 'todo'
            ? { ...col, tasks: [tempCard, ...col.tasks] }
            : col
        );
      }
      return prev;
    });
  }, []);

  // Remove temporary card (used by Turbo mode)
  const removeTemporaryCard = useCallback((tempCardId: string) => {
    setTaskColumns(prev => prev.map(col => ({
      ...col,
      tasks: col.tasks.filter(task => task.id !== tempCardId)
    })));
  }, []);

  return {
    projects,
    taskColumns,
    isLoading,
    error,
    fetchProjects,
    refreshProjects,
    invalidateCache,
    createProject,
    updateProject,
    moveTask,
    addComment,
    deleteProject,
    createTask,
    updateTask,
    deleteTask,
    handleFileUploaded,
    handleFileDeleted,
    handleTeamUpdated,
    addTemporaryCard,
    removeTemporaryCard
  };
}
