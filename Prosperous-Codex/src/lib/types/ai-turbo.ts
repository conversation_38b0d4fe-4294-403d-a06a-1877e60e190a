// Re-export types from ai-turbo-service for consistency
export type { TurboResult, TurboMainTask, TurboSubtask, TurboOptions } from '@/lib/services/ai-turbo-service';
import type { TurboResult } from '@/lib/services/ai-turbo-service';

// Additional types for API requests and responses
export interface TurboRequest {
  inputText: string;
  context?: string;
  tone?: string;
  maxTokens?: number;
}

export interface TurboResponse {
  success: boolean;
  data?: TurboResult;
  error?: string;
}

// Validation schemas using Zod
import { z } from 'zod';

export const TurboSubtaskSchema = z.object({
  id: z.string().min(1, 'Subtask id is required'),
  description: z.string()
});

export const TurboMainTaskSchema = z.object({
  title: z.string().min(1, 'Task title is required'),
  description: z.string(),
  subtasks: z.array(TurboSubtaskSchema).default([])
});

export const TurboResultSchema = z.object({
  title: z.string().default(''),
  eventLog: z.string().default(''),
  description: z.string().default(''),
  tasks: z.array(TurboMainTaskSchema).default([]),
  enhancedText: z.string(),
  originalText: z.string(),
  modelUsed: z.string().optional(),
  tokensUsed: z.number().optional(),
  reasoningContent: z.string().optional()
});

export const TurboRequestSchema = z.object({
  inputText: z.string().min(1, 'Input text is required').max(50000, 'Input text too long'),
  context: z.string().optional().default('task_description'),
  tone: z.string().optional().default('professional'),
  maxTokens: z.number().optional()
});

// Status response type
export interface TurboServiceStatus {
  available: boolean;
  configured: boolean;
  provider: 'openrouter' | 'openai' | 'none';
  models: string[];
}
