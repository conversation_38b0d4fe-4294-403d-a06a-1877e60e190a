// Re-export types from ai-parsing-service for consistency
export type { ParsedContent, ParsedMainTask, ParsedSubtask } from '@/lib/services/ai-parsing-service';
import type { ParsedContent } from '@/lib/services/ai-parsing-service';

// Additional types for API responses
export interface ParseContentRequest {
  inputText: string;
}

export interface ParseContentResponse {
  success: boolean;
  data?: ParsedContent;
  error?: string;
}

// Validation schemas using Zod
import { z } from 'zod';

export const ParsedSubtaskSchema = z.object({
  id: z.string().min(1, 'Subtask id is required'),
  description: z.string()
});

export const ParsedMainTaskSchema = z.object({
  title: z.string().min(1, 'Task title is required'),
  description: z.string(),
  subtasks: z.array(ParsedSubtaskSchema).default([])
});

export const ParsedContentSchema = z.object({
  title: z.string().default(''),
  eventLog: z.string().default(''),
  description: z.string().default(''),
  tasks: z.array(ParsedMainTaskSchema).default([])
});

export const ParseContentRequestSchema = z.object({
  inputText: z.string().min(1, 'Input text is required').max(50000, 'Input text too long')
});
