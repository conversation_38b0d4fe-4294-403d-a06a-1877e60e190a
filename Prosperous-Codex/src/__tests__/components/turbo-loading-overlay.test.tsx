/**
 * Turbo Loading Overlay Tests
 * 
 * Tests for the turbo mode loading overlay component
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { TurboLoadingOverlay } from '@/components/task-master/turbo-loading-overlay';

describe('TurboLoadingOverlay', () => {
  it('should not render when not visible', () => {
    const { container } = render(
      <TurboLoadingOverlay
        isVisible={false}
        progressText="Processing..."
        attempt={1}
      />
    );

    expect(container.firstChild).toBeNull();
  });

  it('should render when visible', () => {
    render(
      <TurboLoadingOverlay
        isVisible={true}
        progressText="Processing with AI..."
        attempt={1}
      />
    );

    expect(screen.getByText('Processing with AI...')).toBeInTheDocument();
  });

  it('should show attempt number for retry attempts', () => {
    render(
      <TurboLoadingOverlay
        isVisible={true}
        progressText="Retry attempt (2/2)"
        attempt={2}
      />
    );

    expect(screen.getByText('Retry attempt (2/2)')).toBeInTheDocument();
    expect(screen.getByText('Attempt 2 of 2')).toBeInTheDocument();
  });

  it('should not show attempt number for first attempt', () => {
    render(
      <TurboLoadingOverlay
        isVisible={true}
        progressText="Processing with AI..."
        attempt={1}
      />
    );

    expect(screen.getByText('Processing with AI...')).toBeInTheDocument();
    expect(screen.queryByText('Attempt 1 of 2')).not.toBeInTheDocument();
  });

  it('should have correct styling classes', () => {
    const { container } = render(
      <TurboLoadingOverlay
        isVisible={true}
        progressText="Processing..."
        attempt={1}
      />
    );

    const overlay = container.firstChild as HTMLElement;
    expect(overlay).toHaveClass('absolute', 'inset-0', 'z-10', 'backdrop-blur-sm');
  });

  it('should show loading spinner', () => {
    render(
      <TurboLoadingOverlay
        isVisible={true}
        progressText="Processing..."
        attempt={1}
      />
    );

    // Check for spinner by looking for the animate-spin class
    const spinner = document.querySelector('.animate-spin');
    expect(spinner).toBeInTheDocument();
  });

  it('should use purple color scheme', () => {
    render(
      <TurboLoadingOverlay
        isVisible={true}
        progressText="Processing..."
        attempt={1}
      />
    );

    // Check for purple color classes
    const progressText = screen.getByText('Processing...');
    expect(progressText).toHaveClass('text-purple-600');
  });
});
