/**
 * Turbo Mode Feature Tests
 * 
 * Integration tests for the complete turbo mode workflow
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';

// Mock the API calls
global.fetch = jest.fn();

// Mock next-auth
jest.mock('next-auth/react', () => ({
  useSession: () => ({
    data: {
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>'
      }
    }
  })
}));

// Mock the router
jest.mock('@/i18n/navigation', () => ({
  useRouter: () => ({
    push: jest.fn()
  })
}));

// Mock translations
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => key
}));

// Mock the hooks
jest.mock('@/hooks/use-task-master', () => ({
  useTaskMaster: () => ({
    taskColumns: [
      {
        id: 'todo',
        title: 'To Do',
        variant: 'neutral',
        icon: 'inbox',
        tasks: []
      }
    ],
    isLoading: false,
    error: null,
    createProject: jest.fn().mockResolvedValue(true),
    refreshProjects: jest.fn(),
    projects: []
  })
}));

jest.mock('@/hooks/use-drag-state', () => ({
  useDragState: () => ({
    startDrag: jest.fn(),
    endDrag: jest.fn(),
    isDragging: false,
    draggedItem: null
  })
}));

jest.mock('@/hooks/use-column-height-sync', () => ({
  useColumnHeightSync: () => ({
    synchronizedHeight: 600,
    registerColumnRef: () => jest.fn(),
    recalculateHeight: jest.fn()
  })
}));

jest.mock('@/hooks/use-enhanced-card-animation', () => ({
  useEnhancedCardAnimation: () => ({
    currentAnimation: null,
    animateCardMovement: jest.fn()
  })
}));

jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn()
  })
}));

// Mock DnD
jest.mock('react-dnd', () => ({
  DndProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useDrop: () => [{ isOver: false, canDrop: false }, jest.fn()],
  useDrag: () => [{ isDragging: false }, jest.fn(), jest.fn()]
}));

jest.mock('react-dnd-html5-backend', () => ({
  HTML5Backend: {}
}));

describe('Turbo Mode Feature', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockClear();
  });

  it('should show turbo button with correct styling', async () => {
    // Dynamic import to avoid module loading issues
    const TaskMasterPage = (await import('@/app/[locale]/(protected)/task-master/page')).default;
    
    render(<TaskMasterPage />);

    // Look for the turbo button (Zap icon)
    const turboButtons = screen.getAllByRole('button');
    const turboButton = turboButtons.find(button => 
      button.querySelector('svg') && 
      button.title?.includes('Turbo Mode')
    );

    expect(turboButton).toBeInTheDocument();
    expect(turboButton).toHaveClass('text-purple-600');
  });

  it('should disable turbo button when input is empty', async () => {
    const TaskMasterPage = (await import('@/app/[locale]/(protected)/task-master/page')).default;
    
    render(<TaskMasterPage />);

    const turboButtons = screen.getAllByRole('button');
    const turboButton = turboButtons.find(button => 
      button.title?.includes('Turbo Mode')
    );

    expect(turboButton).toBeDisabled();
  });

  it('should handle successful turbo API response', async () => {
    // Mock successful API response
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: {
          title: 'Test Project',
          description: 'Enhanced description',
          eventLog: 'Event log content',
          tasks: [],
          reasoningContent: 'AI reasoning'
        }
      })
    });

    const TaskMasterPage = (await import('@/app/[locale]/(protected)/task-master/page')).default;
    
    render(<TaskMasterPage />);

    // Find and fill the input
    const textarea = screen.getByPlaceholderText(/newTask.placeholder/);
    fireEvent.change(textarea, { target: { value: 'Test task input' } });

    // Find and click turbo button
    const turboButtons = screen.getAllByRole('button');
    const turboButton = turboButtons.find(button => 
      button.title?.includes('Turbo Mode')
    );

    expect(turboButton).not.toBeDisabled();
    fireEvent.click(turboButton!);

    // Verify API was called
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/task-master/ai-turbo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputText: 'Test task input',
          context: 'task_description',
          tone: 'professional'
        })
      });
    });
  });

  it('should handle API failure with retry logic', async () => {
    // Mock first call failure, second call success
    (global.fetch as jest.Mock)
      .mockRejectedValueOnce(new Error('JSON parsing error'))
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            title: 'Test Project',
            description: 'Enhanced description',
            eventLog: 'Event log content',
            tasks: []
          }
        })
      });

    const TaskMasterPage = (await import('@/app/[locale]/(protected)/task-master/page')).default;
    
    render(<TaskMasterPage />);

    // Find and fill the input
    const textarea = screen.getByPlaceholderText(/newTask.placeholder/);
    fireEvent.change(textarea, { target: { value: 'Test task input' } });

    // Find and click turbo button
    const turboButtons = screen.getAllByRole('button');
    const turboButton = turboButtons.find(button => 
      button.title?.includes('Turbo Mode')
    );

    fireEvent.click(turboButton!);

    // Verify both API calls were made
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(2);
    });
  });

  it('should not retry for authentication errors', async () => {
    // Mock authentication error
    (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('API key authentication failed'));

    const TaskMasterPage = (await import('@/app/[locale]/(protected)/task-master/page')).default;
    
    render(<TaskMasterPage />);

    // Find and fill the input
    const textarea = screen.getByPlaceholderText(/newTask.placeholder/);
    fireEvent.change(textarea, { target: { value: 'Test task input' } });

    // Find and click turbo button
    const turboButtons = screen.getAllByRole('button');
    const turboButton = turboButtons.find(button => 
      button.title?.includes('Turbo Mode')
    );

    fireEvent.click(turboButton!);

    // Verify only one API call was made (no retry)
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(1);
    });
  });
});
