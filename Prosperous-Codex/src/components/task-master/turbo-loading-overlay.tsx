"use client";

import { Loader2 } from 'lucide-react';

interface TurboLoadingOverlayProps {
  isVisible: boolean;
  progressText: string;
  attempt: number;
}

export function TurboLoadingOverlay({ isVisible, progressText, attempt }: TurboLoadingOverlayProps) {
  if (!isVisible) return null;

  return (
    <div className="absolute inset-0 z-10 flex items-center justify-center bg-white/80 dark:bg-black/80 backdrop-blur-sm rounded-lg">
      <div className="flex flex-col items-center gap-3 p-4">
        {/* Spinning loader */}
        <Loader2 className="h-6 w-6 animate-spin text-purple-600 dark:text-purple-400" />
        
        {/* Progress text */}
        <div className="text-center">
          <p className="text-sm font-medium text-purple-600 dark:text-purple-400">
            {progressText}
          </p>
          {attempt > 1 && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Attempt {attempt} of 2
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
