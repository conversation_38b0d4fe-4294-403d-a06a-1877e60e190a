/**
 * AI Turbo Service Tests
 * 
 * Tests for the combined AI enhancement and parsing service
 */

import { AITurboService } from '@/lib/services/ai-turbo-service';

describe('AITurboService', () => {
  let service: AITurboService;

  beforeEach(() => {
    service = new AITurboService();
  });

  describe('Service Initialization', () => {
    it('should initialize without throwing errors', () => {
      expect(() => new AITurboService()).not.toThrow();
    });

    it('should have correct model configuration', () => {
      expect(service.getAvailableModels()).toEqual([
        'google/gemini-2.5-flash-lite-preview-06-17',
        'deepseek/deepseek-chat-v3-0324'
      ]);
    });

    it('should report provider correctly', () => {
      const provider = service.getProvider();
      expect(['openrouter', 'openai', 'none']).toContain(provider);
    });
  });

  describe('Service Availability', () => {
    it('should check if service is configured', () => {
      const isConfigured = service.isServiceConfigured();
      expect(typeof isConfigured).toBe('boolean');
    });

    it('should check if service is available', () => {
      const isAvailable = service.isAvailable();
      expect(typeof isAvailable).toBe('boolean');
    });
  });

  describe('Input Validation', () => {
    it('should handle empty input gracefully', async () => {
      if (!service.isAvailable()) {
        console.log('Skipping test - AI service not available');
        return;
      }

      await expect(service.enhanceAndParse('')).rejects.toThrow();
    });

    it('should validate turbo options', () => {
      const options = {
        max_tokens: 1000,
        context: 'task_description',
        tone: 'professional'
      };

      expect(options.max_tokens).toBe(1000);
      expect(options.context).toBe('task_description');
      expect(options.tone).toBe('professional');
    });
  });

  describe('Response Structure', () => {
    it('should define correct TurboResult interface structure', () => {
      // This test validates the TypeScript interface structure
      const mockResult = {
        title: 'Test Title',
        eventLog: 'Test Event Log',
        description: 'Test Description',
        tasks: [
          {
            title: 'Task 1',
            description: 'Task 1 Description',
            subtasks: [
              {
                id: '1.1',
                description: 'Subtask 1.1'
              }
            ]
          }
        ],
        enhanced_text: 'Enhanced text content',
        original_text: 'Original input text',
        model_used: 'test-model',
        tokens_used: 100,
        reasoning_content: 'AI reasoning process'
      };

      // Validate structure
      expect(mockResult).toHaveProperty('title');
      expect(mockResult).toHaveProperty('eventLog');
      expect(mockResult).toHaveProperty('description');
      expect(mockResult).toHaveProperty('tasks');
      expect(mockResult).toHaveProperty('enhanced_text');
      expect(mockResult).toHaveProperty('original_text');
      expect(Array.isArray(mockResult.tasks)).toBe(true);
      
      if (mockResult.tasks.length > 0) {
        expect(mockResult.tasks[0]).toHaveProperty('title');
        expect(mockResult.tasks[0]).toHaveProperty('description');
        expect(mockResult.tasks[0]).toHaveProperty('subtasks');
        expect(Array.isArray(mockResult.tasks[0].subtasks)).toBe(true);
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle service unavailable gracefully', async () => {
      // Mock service as unavailable
      jest.spyOn(service, 'isAvailable').mockReturnValue(false);

      await expect(service.enhanceAndParse('test input')).rejects.toThrow(
        'AI Turbo Service is not available'
      );
    });
  });

  describe('Integration with Existing Services', () => {
    it('should use same model configuration as individual services', () => {
      const models = service.getAvailableModels();
      
      // Should match the models used in AIEnhancementService and AIParsingService
      expect(models).toContain('google/gemini-2.5-flash-lite-preview-06-17');
      expect(models).toContain('deepseek/deepseek-chat-v3-0324');
    });

    it('should support same reasoning configuration', () => {
      // Test that reasoning configuration is properly set up
      // This is tested indirectly through the service initialization
      expect(service.isServiceConfigured).toBeDefined();
    });
  });
});

// Mock tests for when AI service is not available
describe('AITurboService - Mock Tests', () => {
  it('should parse turbo response correctly', () => {
    const service = new AITurboService();
    
    // Mock response that would come from LLM
    const mockLLMResponse = `
<thinking>
This is the AI reasoning process...
</thinking>

Subject: Project Briefing: Test Project // Test Client

---EventLog---
Event Log:
- 01/01/2024 - User (Test): Initial project request
---EndOfEventLog---

---Description---
This is a test project description with enhanced content.
---EndOfDescription---

---TaskAssignment---
Task Assignment:
» Task 1: Setup Development Environment
  - Subtasks:
    - 1.1: Install Node.js and npm
    - 1.2: Configure development database
---EndOfTaskAssignment---

{
  "title": "Project Briefing: Test Project // Test Client",
  "eventLog": "Event Log:\\n- 01/01/2024 - User (Test): Initial project request",
  "description": "This is a test project description with enhanced content.",
  "tasks": [
    {
      "title": "» Task 1: Setup Development Environment",
      "description": "Setup Development Environment",
      "subtasks": [
        {
          "id": "1.1",
          "description": "Install Node.js and npm"
        },
        {
          "id": "1.2",
          "description": "Configure development database"
        }
      ]
    }
  ]
}`;

    // Test the parsing logic (this would be called internally)
    // For now, just validate the mock structure
    expect(mockLLMResponse).toContain('<thinking>');
    expect(mockLLMResponse).toContain('---EventLog---');
    expect(mockLLMResponse).toContain('---Description---');
    expect(mockLLMResponse).toContain('---TaskAssignment---');
    expect(mockLLMResponse).toContain('"title":');
    expect(mockLLMResponse).toContain('"tasks":');
  });
});
