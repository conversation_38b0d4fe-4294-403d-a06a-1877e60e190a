import OpenAI from 'openai';

/**
 * AI Turbo Service
 *
 * Combines text enhancement and JSON parsing into a single LLM operation.
 * Uses a unified prompt that instructs the model to first generate a detailed
 * project briefing and then immediately parse that briefing into a structured
 * JSON object, returning both in a single, delimited response.
 */

export interface TurboOptions {
  max_tokens?: number;
  context?: string;
  tone?: string;
}

export interface TurboResult {
  title: string;
  eventLog: string;
  description: string;
  tasks: TurboMainTask[];
  enhancedText: string; // The full, human-readable enhanced text
  originalText: string;
  modelUsed?: string;
  tokensUsed?: number;
  reasoningContent?: string;
}

export interface TurboMainTask {
  title: string;
  description: string;
  subtasks: TurboSubtask[];
}

export interface TurboSubtask {
  id: string;
  description: string;
}

export class AITurboService {
  private client: OpenAI | null = null;
  private isConfigured: boolean = false;
  private readonly primaryModel = 'google/gemini-2.5-flash-lite-preview-06-17';
  private readonly fallbackModel = 'deepseek/deepseek-chat-v3-0324';

  constructor() {
    this.initializeClient();
  }

  /**
   * Initialize OpenAI-compatible client with environment configuration
   */
  private initializeClient(): void {
    try {
      const openRouterKey = process.env.OPENROUTER_API_KEY;
      const openAiKey = process.env.OPENAI_API_KEY;

      if (!openRouterKey && !openAiKey) {
        return;
      }

      const apiKey = openRouterKey || openAiKey;
      const baseURL = openRouterKey ? 'https://openrouter.ai/api/v1' : undefined;

      this.client = new OpenAI({
        apiKey: apiKey,
        baseURL: baseURL,
        defaultHeaders: baseURL ? {
          'HTTP-Referer': process.env.OPENROUTER_REFERER || 'http://localhost:3000',
          'X-Title': process.env.OPENROUTER_TITLE || 'Prosperous Codex'
        } : undefined
      });

      this.isConfigured = true;
    } catch (error) {
      console.error('Failed to initialize AI Turbo Service:', error);
      this.isConfigured = false;
    }
  }

  /**
   * Check if the service is available and properly configured
   */
  public isAvailable(): boolean {
    return this.client !== null && this.isConfigured;
  }

  /**
   * Enhance and parse content in a single LLM call
   */
  public async enhanceAndParse(
    inputText: string,
    options: TurboOptions = {}
  ): Promise<TurboResult> {
    if (!this.isAvailable()) {
      throw new Error('AI Turbo Service is not available. Please check API key configuration.');
    }

    const {
      max_tokens = parseInt(process.env.AI_MAX_TOKENS || '65536'),
    } = options;

    const systemPrompt = this.buildTurboSystemPrompt();
    const userPrompt = `---EmailThread---
${inputText}`;

    let completion;
    let modelUsed = this.primaryModel;

    try {
      completion = await this.client!.chat.completions.create({
        model: this.primaryModel,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: max_tokens,
        temperature: 0.65,
      });
    } catch (primaryError) {
      console.warn('Primary model failed, trying fallback:', primaryError);
      modelUsed = this.fallbackModel;
      try {
        completion = await this.client!.chat.completions.create({
          model: this.fallbackModel,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          max_tokens: max_tokens,
          temperature: 0.65,
        });
      } catch (fallbackError) {
        throw new Error('All AI models failed to process the request');
      }
    }

    try {
      const responseText = completion.choices[0]?.message?.content;
      const tokensUsed = completion.usage?.total_tokens;

      if (!responseText) {
        throw new Error('No response received from AI service');
      }

      const parsedResponse = this.parseAndValidateTurboResponse(responseText, inputText);
      parsedResponse.modelUsed = modelUsed;
      parsedResponse.tokensUsed = tokensUsed;

      return parsedResponse;

    } catch (error) {
      if (error instanceof OpenAI.APIError) {
        if (error.status === 429) {
          throw new Error('Rate limit exceeded. Please try again later.');
        }
        if (error.status === 401) {
          throw new Error('API key authentication failed');
        }
        if (error.status === 400 && error.message.includes('content_filter')) {
          throw new Error('Content cannot be processed due to safety guidelines');
        }
      }

      throw new Error(`AI turbo processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Build the unified system prompt that combines enhancement and parsing
   */
  private buildTurboSystemPrompt(): string {
    // This prompt now includes all detailed instructions directly.
    return `**Prompt: AI Turbo Service - Unified Briefing Generation and JSON Parsing**

**Mandatory Protocol:** Your process must be three distinct steps, completed in a single response.
1. **Reasoning Step:** Encapsulate your entire reasoning process within a \`<thinking>...</thinking>\` block. This block must contain ONLY your analysis and planning.
2. **Enhancement Step:** After closing the thinking block, generate the full, human-readable project briefing document based on your reasoning, following all guidelines in Phase 1.
3. **Parsing Step:** After generating the briefing, you must add a unique separator \`---JSON_SEPARATOR---\` on a new line, followed immediately by the final JSON object.

It is a critical failure to mix the content of these steps.

---

### **PHASE 1: ENHANCEMENT GUIDELINES (Internal Generation)**

You are a bilingual Project Management Assistant for Prosperous Printing Company Limited.

**Guiding Principles:**
- **Be Factual:** Base all output strictly on the provided input. Do not invent details.
- **Handle Vague Inputs:** If the input is too brief to create a meaningful briefing (e.g., "new website"), state this in the description and create a basic task structure for the user to fill out. Do not hallucinate.
- **Language Detection:** If the user's input text contains more than 33% Chinese characters, your entire response must be in Traditional Chinese. Otherwise, it must be in English.

**Terminology (for Traditional Chinese output):**
| English Term | Traditional Chinese Term |
| :--- | :--- |
| Subject | 主題 |
| Project | 項目 |
| Project Briefing | 項目簡報 |
| Event Log | 事件日誌 |
| Job Description & Current Status | 工作描述與目前狀態 |
| Task Assignment | 任務分配 |
| Key Focus | 核心重點 |
| Event Background | 事件背景 |
| Core Problem | 核心問題 |
| Top Priority | 首要任務 |
| Task | 任務 |
| Description | 描述 |
| Subtasks | 分支任務 |
| Company | 公司 |
| Client | 客戶 |
| Supplier | 供應商 |

**Enhancement Output Structure:**
- **Subject:** Project Briefing: [Project Name] // [Client Name]
- The section markers (\`---EventLog---\`, \`---Description---\`, \`---TaskAssignment---\`, and their corresponding end markers) MUST be output exactly as written and must NOT be translated.

---EventLog---
[Content generated according to Event Log rules]
---EndOfEventLog---

---Description---
[Content generated according to Description rules]
---EndOfDescription---

---TaskAssignment---
[Content generated according to Task Assignment rules]
---EndOfTaskAssignment---

**Detailed Content Generation Rules:**

**Event Log Rules:**
- Read the \`---EmailThread---\` and any existing \`---EventLog---\`.
- Identify any new material events from the emails that are not already in the log.
- When creating a concise event summary, the goal is a high-level overview of the action.
- Combine the old log entries with the new entries.
- Output the complete and updated event log below this point in chronological order.
- Each entry must start with a dash (-).
- When determining the [Affiliation], use a concise, common-sense short name (e.g., use 'Prosperous' for 'Prosperous Printing Company Limited'). Avoid using the full legal name.
- Format: \`- DD/MM/YYYY - [Sender Name] ([Affiliation]): [Concise summary of past event.]\`

**Description Rules:**
- Choose a suitable header/title for this description section from the following options: 'Review', 'Profile', 'Detail', 'Depiction', 'Explanation', 'Narrative', or 'Description', based on what best fits the content.
- Synthesize all user-provided input (including the Email Thread, Event Log, and any other context) into a detailed narrative, adhering to the Guiding Principles above.
- Primary goal: Maximize clarity and detail, but only when supported by the input data, DO NOT fabricate details.
- **Use paragraphs** for the main narrative, but whenever listing multiple issues, requirements, or key data points, break them out into an indented bulleted list using - to improve readability.
- Narrative must cover:
  - Project's overall goal
  - Summary of how the situation evolved
  - Detailed explanation of the core problem/trigger
  - Clear statement of the immediate objective

**Task Assignment Rules:**
- The Task Title must be a concise, high-level summary. It should convey the main goal without going into the detailed context.
- **Do NOT assign tasks to anyone**
- Use the indented structure below to ensure clear visual hierarchy. Subtasks should be offset from their parent task to improve structural clarity.
- Write a brief Description for each main task (narrative explaining goal, importance, and deadline)
- List subtasks using Task.Subtask numbering (e.g., 1.1, 1.2, 2.1)
- Provide a brief Description for each subtask (explaining specific action required)
- Use no descriptions **only** for extremely simple, self explanatory.

Format:
\`\`\`
» Task 1: [Action-focused Task Name]
- Description: [Concise narrative explaining objective, importance, and deadline. E.g., "The objective of this task is to... This is important because... "]
  - Subtasks:
    - 1.1: [Subtask Title]
      - Description: [Concise narrative explaining specific action]
    - 1.2: [Subtask Title]
       - Description: [Concise narrative explaining specific action]

[Repeat structure for additional tasks]
\`\`\`

---

### **PHASE 2: JSON GENERATION REQUIREMENTS**

After generating the complete enhanced text from Phase 1, add the separator and then generate a single, valid JSON object that parses the text you just wrote.

**JSON Schema:**
\`\`\`json
{
  "title": "string",
  "eventLog": "string",
  "description": "string",
  "tasks": [
    {
      "title": "string",
      "description": "string",
      "subtasks": [
        {
          "id": "string",
          "description": "string"
        }
      ]
    }
  ]
}
\`\`\`

**JSON Parsing Rules:**
1. **title**: Extract from the "Subject:" line.
2. **eventLog**: Extract the pure content between \`---EventLog---\` and \`---EndOfEventLog---\` (exclude the markers themselves).
3. **description**: Extract the pure content between \`---Description---\` and \`---EndOfDescription---\` (exclude the markers themselves).
4. **tasks**: Parse the content between \`---TaskAssignment---\` and \`---EndOfTaskAssignment---\`. For each main task (lines starting with "»"), create a task object. For subtasks (lines with numbering like 1.1, 1.2), create subtask objects with the numbering as the ID. Preserve all task and subtask descriptions.

**Final Output Requirements:**
- Your response must contain the thinking block, then the enhanced briefing, then the separator, then the JSON.
- The JSON must be valid and parseable.
- Do not include markdown code fences around the JSON.
- Ensure the separator \`---JSON_SEPARATOR---\` appears exactly once on its own line.`;
  }

  /**
   * Parse and validate the turbo response containing both enhanced text and JSON
   */
  private parseAndValidateTurboResponse(responseText: string, originalText: string): TurboResult {
    let reasoningContent: string | undefined;
    let cleanResponse = responseText;

    // Extract thinking content if present
    const thinkingMatch = responseText.match(/<thinking>([\s\S]*?)<\/thinking>/);
    if (thinkingMatch) {
      reasoningContent = thinkingMatch[1].trim();
      cleanResponse = responseText.replace(/<thinking>[\s\S]*?<\/thinking>/, '').trim();
    }

    // Split the response into enhanced text and JSON using the separator
    const parts = cleanResponse.split('---JSON_SEPARATOR---');
    if (parts.length !== 2) {
      throw new Error('Invalid turbo response format: JSON separator not found or found multiple times.');
    }

    const enhancedText = parts[0].trim();
    const jsonText = parts[1].trim();

    if (!enhancedText || !jsonText) {
        throw new Error('Enhanced text or JSON part is empty in the turbo response.');
    }

    try {
      const parsed = JSON.parse(jsonText);

      // Validate and sanitize structure
      const result: TurboResult = {
        title: typeof parsed.title === 'string' ? parsed.title : '',
        eventLog: typeof parsed.eventLog === 'string' ? parsed.eventLog : '',
        description: typeof parsed.description === 'string' ? parsed.description : '',
        tasks: Array.isArray(parsed.tasks) ? parsed.tasks.map((task: any) => ({
          title: typeof task.title === 'string' ? task.title : '',
          description: typeof task.description === 'string' ? task.description : '',
          subtasks: Array.isArray(task.subtasks) ? task.subtasks.map((subtask: any) => ({
            id: typeof subtask.id === 'string' ? subtask.id : '',
            description: typeof subtask.description === 'string' ? subtask.description : ''
          })) : []
        })) : [],
        enhancedText: enhancedText,
        originalText: originalText,
        reasoningContent: reasoningContent
      };

      return result;
    } catch (parseError) {
      throw new Error(`Failed to parse JSON from turbo response: ${parseError instanceof Error ? parseError.message : 'Unknown error'}`);
    }
  }
}

// Export singleton instance
export const aiTurboService = new AITurboService();
