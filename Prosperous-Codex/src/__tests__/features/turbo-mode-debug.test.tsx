/**
 * Turbo Mode Debug Tests
 * 
 * Tests to verify the turbo mode implementation fixes
 */

import { FieldMapper } from '@/lib/task-master/field-mapping';

describe('Turbo Mode Data Flow Debug', () => {
  describe('Field Mapping', () => {
    it('should correctly map turbo response fields from snake_case to camelCase', () => {
      const turboServiceResponse = {
        title: 'Test Project',
        event_log: 'Event log content',
        description: 'Project description',
        tasks: [
          {
            title: 'Task 1',
            description: 'Task description',
            subtasks: [
              { id: '1.1', description: 'Subtask 1' }
            ]
          }
        ],
        enhanced_text: 'Enhanced content',
        original_text: 'Original input',
        model_used: 'test-model',
        tokens_used: 100,
        reasoning_content: 'AI reasoning'
      };

      const apiResponse = FieldMapper.dbToApi(turboServiceResponse);

      expect(apiResponse).toEqual({
        title: 'Test Project',
        eventLog: 'Event log content',
        description: 'Project description',
        tasks: [
          {
            title: 'Task 1',
            description: 'Task description',
            subtasks: [
              { id: '1.1', description: 'Subtask 1' }
            ]
          }
        ],
        enhancedText: 'Enhanced content',
        originalText: 'Original input',
        modelUsed: 'test-model',
        tokensUsed: 100,
        reasoningContent: 'AI reasoning'
      });
    });

    it('should correctly map project data for createProject', () => {
      const turboApiResponse = {
        title: 'Test Project',
        eventLog: 'Event log content',
        description: 'Project description',
        tasks: [
          {
            title: 'Task 1',
            description: 'Task description',
            subtasks: [
              { id: '1.1', description: 'Subtask 1' }
            ]
          }
        ]
      };

      const projectData = {
        title: turboApiResponse.title || 'New Task',
        fullDescription: turboApiResponse.description || '',
        eventLog: turboApiResponse.eventLog || '',
        status: 'todo' as const,
        priority: 'medium' as const,
        progress: 0,
        tasks: turboApiResponse.tasks || []
      };

      expect(projectData.title).toBe('Test Project');
      expect(projectData.fullDescription).toBe('Project description');
      expect(projectData.eventLog).toBe('Event log content');
      expect(projectData.tasks).toHaveLength(1);
      expect(projectData.tasks[0].title).toBe('Task 1');
    });
  });

  describe('Temporary Card Structure', () => {
    it('should create a valid temporary card structure', () => {
      const tempId = `temp-turbo-${Date.now()}`;
      const tempCard = {
        id: tempId,
        title: 'Processing...',
        description: '',
        status: 'todo' as const,
        priority: 'medium' as const,
        dueDate: undefined,
        visibility: 'public' as const,
        assignedTo: undefined,
        createdBy: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        progress: 0,
        taskCount: 0,
        completedTaskCount: 0,
        projectDetails: {
          fullDescription: '',
          eventLog: '',
          fullDescriptionLastEditedBy: null,
          fullDescriptionLastEditedAt: null,
          eventLogLastEditedBy: null,
          eventLogLastEditedAt: null
        }
      };

      expect(tempCard.id).toMatch(/^temp-turbo-\d+$/);
      expect(tempCard.title).toBe('Processing...');
      expect(tempCard.status).toBe('todo');
      expect(tempCard.projectDetails).toBeDefined();
      expect(tempCard.projectDetails.eventLog).toBe('');
    });
  });

  describe('API Response Structure', () => {
    it('should match expected turbo API response structure', () => {
      const mockApiResponse = {
        success: true,
        data: {
          title: 'Test Project',
          eventLog: 'Event log content',
          description: 'Project description',
          tasks: [
            {
              title: 'Task 1',
              description: 'Task description',
              subtasks: [
                { id: '1.1', description: 'Subtask 1' }
              ]
            }
          ],
          enhancedText: 'Enhanced content',
          originalText: 'Original input',
          modelUsed: 'test-model',
          tokensUsed: 100,
          reasoningContent: 'AI reasoning'
        }
      };

      expect(mockApiResponse.success).toBe(true);
      expect(mockApiResponse.data.title).toBeDefined();
      expect(mockApiResponse.data.eventLog).toBeDefined();
      expect(mockApiResponse.data.description).toBeDefined();
      expect(mockApiResponse.data.tasks).toBeDefined();
      expect(Array.isArray(mockApiResponse.data.tasks)).toBe(true);
    });
  });

  describe('Data Flow Validation', () => {
    it('should validate complete turbo mode data flow', () => {
      // 1. User input
      const userInput = 'Create a new website project';

      // 2. Temporary card creation
      const tempId = `temp-turbo-${Date.now()}`;
      const tempCard = {
        id: tempId,
        title: 'Processing...',
        projectDetails: {
          fullDescription: '',
          eventLog: ''
        }
      };

      // 3. Mock turbo API response (after field mapping)
      const turboResponse = {
        title: 'Project Briefing: Website Development // Client Name',
        eventLog: '- 01/01/2024 - User (Client): Requested new website project',
        description: 'Detailed project description...',
        tasks: [
          {
            title: 'Setup Development Environment',
            description: 'Initialize project structure',
            subtasks: [
              { id: '1.1', description: 'Install dependencies' },
              { id: '1.2', description: 'Configure build tools' }
            ]
          }
        ]
      };

      // 4. Project data for createProject
      const projectData = {
        title: turboResponse.title,
        fullDescription: turboResponse.description,
        eventLog: turboResponse.eventLog,
        status: 'todo' as const,
        priority: 'medium' as const,
        progress: 0,
        tasks: turboResponse.tasks
      };

      // Validate the complete flow
      expect(tempCard.id).toMatch(/^temp-turbo-\d+$/);
      expect(projectData.title).toContain('Website Development');
      expect(projectData.eventLog).toContain('Requested new website project');
      expect(projectData.fullDescription).toContain('Detailed project description');
      expect(projectData.tasks).toHaveLength(1);
      expect(projectData.tasks[0].subtasks).toHaveLength(2);
    });
  });
});
