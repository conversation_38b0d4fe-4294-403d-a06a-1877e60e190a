import OpenAI from 'openai';

/**
 * AI Turbo Service
 * 
 * Combines text enhancement and JSON parsing into a single LLM operation.
 * Uses the same models and configuration as the individual services but with
 * a unified prompt that performs both enhancement and parsing in one call.
 */

export interface TurboOptions {
  max_tokens?: number;
  context?: string;
  tone?: string;
}

export interface TurboResult {
  title: string;
  eventLog: string;
  description: string;
  tasks: TurboMainTask[];
  enhanced_text?: string; // The enhanced text before parsing
  original_text: string;
  model_used?: string;
  tokens_used?: number;
  reasoning_content?: string;
}

export interface TurboMainTask {
  title: string;
  description: string;
  subtasks: TurboSubtask[];
}

export interface TurboSubtask {
  id: string;
  description: string;
}

export class AITurboService {
  private client: OpenAI | null = null;
  private isConfigured: boolean = false;
  private readonly primaryModel = 'google/gemini-2.5-flash-lite-preview-06-17';
  private readonly fallbackModel = 'deepseek/deepseek-chat-v3-0324';

  constructor() {
    this.initializeClient();
  }

  /**
   * Initialize OpenAI-compatible client with environment configuration
   */
  private initializeClient(): void {
    try {
      const openRouterKey = process.env.OPENROUTER_API_KEY;
      const openAiKey = process.env.OPENAI_API_KEY;

      if (!openRouterKey && !openAiKey) {
        return;
      }

      if (openRouterKey) {
        this.client = new OpenAI({
          apiKey: openRouterKey,
          baseURL: 'https://openrouter.ai/api/v1',
          defaultHeaders: {
            'HTTP-Referer': process.env.OPENROUTER_REFERER || 'http://localhost:3000',
            'X-Title': process.env.OPENROUTER_TITLE || 'Prosperous Codex'
          }
        });
      } else if (openAiKey) {
        this.client = new OpenAI({
          apiKey: openAiKey
        });
      }

      this.isConfigured = true;
    } catch (error) {
      console.error('Failed to initialize AI Turbo Service:', error);
      this.isConfigured = false;
    }
  }

  /**
   * Check if the service is available and properly configured
   */
  public isAvailable(): boolean {
    return this.client !== null && this.isConfigured;
  }

  /**
   * Check if the service is configured (has API key)
   */
  public isServiceConfigured(): boolean {
    return this.isConfigured;
  }

  /**
   * Get the current AI provider being used
   */
  public getProvider(): 'openrouter' | 'openai' | 'none' {
    if (!this.isConfigured) return 'none';
    return process.env.OPENROUTER_API_KEY ? 'openrouter' : 'openai';
  }

  /**
   * Get available models
   */
  public getAvailableModels(): string[] {
    return [this.primaryModel, this.fallbackModel];
  }

  /**
   * Check if a model supports reasoning configuration
   */
  private supportsReasoning(model: string): boolean {
    const reasoningModels = [
      'google/gemini-2.5-flash-lite-preview-06-17',
    ];
    return reasoningModels.includes(model);
  }

  /**
   * Get reasoning configuration for models that support it
   * Uses thinking_budget parameter for reasoning models
   */
  private getReasoningConfig(model: string) {
    if (this.supportsReasoning(model)) {
      return {
        thinking_budget: 20000 // Allow thinking for enhancement phase
      };
    }
    return undefined;
  }

  /**
   * Enhance and parse content in a single LLM call
   */
  public async enhanceAndParse(
    inputText: string,
    options: TurboOptions = {}
  ): Promise<TurboResult> {
    if (!this.isAvailable()) {
      throw new Error('AI Turbo Service is not available. Please check API key configuration.');
    }

    const {
      max_tokens = parseInt(process.env.AI_MAX_TOKENS || '65536'),
      context = 'task_description',
      tone = 'professional'
    } = options;

    const systemPrompt = this.buildTurboSystemPrompt();
    const userPrompt = `--- EVENT LOG ---
None

--- EMAIL THREAD ---
Task Request: ${inputText}`;

    // Try primary model first, then fallback
    let completion;

    try {
      // Build request configuration
      const requestConfig: any = {
        model: this.primaryModel,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: max_tokens,
        temperature: 0.65,
        top_p: 0.65,
        frequency_penalty: 0.1,
        presence_penalty: 0.1
      };

      // Add reasoning configuration for models that support it
      const reasoningConfig = this.getReasoningConfig(this.primaryModel);
      if (reasoningConfig) {
        requestConfig.reasoning = reasoningConfig;
      }

      completion = await this.client!.chat.completions.create(requestConfig);
    } catch (primaryError) {
      console.warn('Primary model failed, trying fallback:', primaryError);

      try {
        // Build fallback request configuration
        const fallbackRequestConfig: any = {
          model: this.fallbackModel,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          max_tokens: max_tokens,
          temperature: 0.65,
          top_p: 0.65,
          frequency_penalty: 0.1,
          presence_penalty: 0.1
        };

        // Add reasoning configuration for fallback model if it supports it
        const fallbackReasoningConfig = this.getReasoningConfig(this.fallbackModel);
        if (fallbackReasoningConfig) {
          fallbackRequestConfig.reasoning = fallbackReasoningConfig;
        }

        completion = await this.client!.chat.completions.create(fallbackRequestConfig);
      } catch (fallbackError) {
        throw new Error('All AI models failed to process the request');
      }
    }

    try {
      const responseText = completion.choices[0]?.message?.content;

      if (!responseText) {
        throw new Error('No response received from AI service');
      }

      // Parse the response to extract enhanced text and JSON
      return this.parseAndValidateTurboResponse(responseText, inputText);

    } catch (error) {
      if (error instanceof OpenAI.APIError) {
        if (error.status === 429) {
          throw new Error('Rate limit exceeded. Please try again later.');
        }
        if (error.status === 401) {
          throw new Error('API key authentication failed');
        }
        if (error.status === 400 && error.message.includes('content_filter')) {
          throw new Error('Content cannot be processed due to safety guidelines');
        }
      }

      throw new Error(`AI turbo processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Build the unified system prompt that combines enhancement and parsing
   */
  private buildTurboSystemPrompt(): string {
    return `**Prompt: AI Turbo Service - Enhanced Project Briefing with JSON Output**

**CRITICAL INSTRUCTION**: This is a two-phase operation that must be completed in a single response:

**Phase 1: Text Enhancement** - Enhance the user's input following the detailed project briefing guidelines
**Phase 2: JSON Parsing** - Convert the enhanced text into structured JSON format

**Mandatory Process:**
1. **Thinking Step**: Encapsulate your entire reasoning process within <thinking>...</thinking> tags
2. **Enhancement Step**: Generate the enhanced project briefing following the detailed format
3. **JSON Step**: Parse the enhanced content into the required JSON structure

---

**PHASE 1: TEXT ENHANCEMENT GUIDELINES**

You are a bilingual Project Management Assistant for Prosperous Printing Company Limited. Analyze the language composition of the input; if the text contains more than 33% Chinese characters, your entire response must be in Traditional Chinese. Otherwise, your entire response must be in English.

**Core Function:** Process event logs and email threads into a detailed briefing document with comprehensive history, thorough description, and clear action items.

**Guiding Principles:**
- **Be Factual:** Base output strictly on provided input. Do not invent details not found in the input.
- **Handle Vague Inputs:** For brief inputs lacking context, state that more information is needed and provide a basic template.
- **Keep Responses Succinct:** When context is limited, the response should be equally limited.

**Output Structure for Enhancement:**
Subject: Project Briefing: [Project Name] // [Client Name]

---EventLog---
Event Log:
[Enhanced event log content based on input]
---EndOfEventLog---

---Description---
[Enhanced description with detailed narrative]
---EndOfDescription---

---TaskAssignment---
Task Assignment:
[Enhanced task structure with main tasks and subtasks]
---EndOfTaskAssignment---

---

**PHASE 2: JSON PARSING REQUIREMENTS**

After generating the enhanced text, you must immediately parse it into this exact JSON structure:

{
  "title": "string",
  "eventLog": "string", 
  "description": "string",
  "tasks": [
    {
      "title": "string",
      "description": "string", 
      "subtasks": [
        {
          "id": "string",
          "description": "string"
        }
      ]
    }
  ]
}

**JSON Parsing Rules:**
1. **title**: Extract from "Subject:" line
2. **eventLog**: Content between ---EventLog--- and ---EndOfEventLog--- (exclude markers)
3. **description**: Content between ---Description--- and ---EndOfDescription--- (exclude markers)  
4. **tasks**: Content between ---TaskAssignment--- and ---EndOfTaskAssignment--- (exclude markers)
   - Main tasks: Lines starting with "»", "Task", or numbered items
   - Subtasks: Indented lines with decimal numbering (1.1, 1.2, etc.)

**FINAL OUTPUT FORMAT:**
Your response must contain:
1. The enhanced project briefing text
2. Followed immediately by the JSON object (no additional text or formatting)

**CRITICAL**: The JSON must be valid and parseable. Do not include markdown code fences or explanatory text around the JSON.`;
  }

  /**
   * Parse and validate the turbo response containing both enhanced text and JSON
   */
  private parseAndValidateTurboResponse(responseText: string, originalText: string): TurboResult {
    // Extract thinking content if present
    let reasoningContent: string | undefined;
    let cleanResponse = responseText;

    const thinkingMatch = responseText.match(/<thinking>([\s\S]*?)<\/thinking>/);
    if (thinkingMatch) {
      reasoningContent = thinkingMatch[1].trim();
      cleanResponse = responseText.replace(/<thinking>[\s\S]*?<\/thinking>/, '').trim();
    }

    // Find the JSON part (should be at the end)
    const jsonMatch = cleanResponse.match(/\{[\s\S]*\}$/);
    if (!jsonMatch) {
      throw new Error('No valid JSON found in turbo response');
    }

    const jsonText = jsonMatch[0];
    const enhancedText = cleanResponse.replace(/\{[\s\S]*\}$/, '').trim();

    try {
      const parsed = JSON.parse(jsonText);
      
      // Validate and sanitize structure
      const result: TurboResult = {
        title: typeof parsed.title === 'string' ? parsed.title : '',
        eventLog: typeof parsed.eventLog === 'string' ? parsed.eventLog : '',
        description: typeof parsed.description === 'string' ? parsed.description : '',
        tasks: Array.isArray(parsed.tasks) ? parsed.tasks.map((task: any) => ({
          title: typeof task.title === 'string' ? task.title : '',
          description: typeof task.description === 'string' ? task.description : '',
          subtasks: Array.isArray(task.subtasks) ? task.subtasks.map((subtask: any) => ({
            id: typeof subtask.id === 'string' ? subtask.id : '',
            description: typeof subtask.description === 'string' ? subtask.description : ''
          })) : []
        })) : [],
        enhanced_text: enhancedText,
        original_text: originalText,
        reasoning_content: reasoningContent
      };

      return result;
    } catch (parseError) {
      throw new Error(`Failed to parse JSON from turbo response: ${parseError instanceof Error ? parseError.message : 'Unknown error'}`);
    }
  }
}

// Export singleton instance
export const aiTurboService = new AITurboService();
